// 测试搜索功能的核心逻辑
// 这个脚本可以在浏览器控制台中运行

// 复制搜索功能中的关键函数
function parseKeywords(keywords) {
  return keywords
    .split(" ")
    .filter((keyword) => {
      return !!keyword;
    })
    .map((keyword) => {
      return keyword.toLowerCase();
    });
}

function generateStarRating(score) {
  if (!score || score <= 0) return "";
  
  // 定义星星数量的阈值
  let starCount;
  if (score >= 20) {
    starCount = 5; // 高匹配度：多个关键词在标题中匹配
  } else if (score >= 15) {
    starCount = 4; // 较高匹配度：标题+摘要匹配
  } else if (score >= 10) {
    starCount = 3; // 中等匹配度：标题匹配或多个摘要匹配
  } else if (score >= 5) {
    starCount = 2; // 较低匹配度：摘要匹配
  } else {
    starCount = 1; // 低匹配度：仅内容匹配
  }
  
  return "★".repeat(starCount);
}

// 模拟搜索数据
const mockSearchData = [
  {
    title: "Hugo Installation Guide",
    content: "This is a comprehensive guide for installing Hugo static site generator. Hugo is a fast and modern static site generator written in Go.",
    summary: "Learn how to install Hugo on your system",
    url: "/posts/hugo-installation/",
    date: "2024-01-01",
    categories: ["Tutorial"],
    tags: ["Hugo", "Installation"]
  },
  {
    title: "Theme Configuration",
    content: "Configure your Hugo theme with custom settings and parameters. This guide covers all the configuration options available.",
    summary: "Complete theme configuration guide",
    url: "/posts/theme-config/",
    date: "2024-01-02",
    categories: ["Guide"],
    tags: ["Theme", "Configuration"]
  },
  {
    title: "Content Management",
    content: "Learn how to manage your content effectively in Hugo. This includes organizing posts, pages, and media files.",
    summary: "Effective content management strategies",
    url: "/posts/content-management/",
    date: "2024-01-03",
    categories: ["Tutorial"],
    tags: ["Content", "Management"]
  }
];

// 模拟搜索算法
function mockSearch(data, query) {
  const keywords = parseKeywords(query);
  const results = [];

  data.forEach((item, index) => {
    let score = 0;
    let hasMatch = false;

    keywords.forEach((keyword) => {
      const keywordLower = keyword.toLowerCase();

      // 检查标题
      if (item.title && item.title.toLowerCase().includes(keywordLower)) {
        score += 10;
        hasMatch = true;
      }

      // 检查内容
      if (item.content && item.content.toLowerCase().includes(keywordLower)) {
        score += 1;
        hasMatch = true;
      }

      // 检查摘要
      if (item.summary && item.summary.toLowerCase().includes(keywordLower)) {
        score += 5;
        hasMatch = true;
      }
    });

    if (hasMatch) {
      results.push({
        ...item,
        score,
        keywords,
        starRating: generateStarRating(score)
      });
    }
  });

  return results.sort((a, b) => b.score - a.score);
}

// 测试用例
const testQueries = [
  "Hugo",           // 应该在标题和内容中匹配
  "Installation",   // 应该在标题中匹配
  "guide",          // 应该在内容和摘要中匹配
  "configuration",  // 应该在内容中匹配
  "theme",          // 应该在标题中匹配
  "content"         // 应该在标题和内容中匹配
];

console.log("=== 搜索功能星星评级测试 ===\n");

testQueries.forEach(query => {
  console.log(`搜索关键词: "${query}"`);
  const results = mockSearch(mockSearchData, query);
  
  if (results.length === 0) {
    console.log("  无搜索结果\n");
    return;
  }
  
  results.forEach((result, index) => {
    console.log(`  ${index + 1}. ${result.title}`);
    console.log(`     分数: ${result.score} | 星星: ${result.starRating}`);
    console.log(`     URL: ${result.url}`);
  });
  console.log("");
});

// 导出函数供浏览器控制台使用
if (typeof window !== 'undefined') {
  window.testSearchLogic = {
    mockSearch,
    generateStarRating,
    parseKeywords,
    mockSearchData,
    testQueries
  };
  console.log("测试函数已导出到 window.testSearchLogic");
}

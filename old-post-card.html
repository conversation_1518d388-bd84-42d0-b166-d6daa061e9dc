<article class="post-card group">
  <a href="{{ .RelPermalink }}" class="post-card-link block">
    <div
      class="bg-card border-border hover:bg-primary/5 hover:border-primary/20 focus:ring-primary/20 relative flex min-h-[200px] flex-col overflow-hidden rounded-xl border transition-all duration-300 ease-out hover:-translate-y-1 hover:scale-[1.02] hover:shadow-lg focus:ring-2 focus:outline-none">
      <!-- 移动端顶部封面 -->
      <div class="block md:hidden">
        {{ if .Params.cover }}
          <div class="aspect-[2/1] overflow-hidden">
            <img
              src="{{ .Params.cover }}"
              alt="{{ .Title }}"
              class="h-full w-full object-cover transition-transform duration-300 group-hover:scale-105"
              loading="lazy" />
          </div>
        {{ else }}
          <!-- 移动端动态默认装饰 -->
          {{ $hash := md5 .Title }}
          {{ $hexColor := substr $hash 0 2 }}
          {{ $hexPattern := substr $hash 2 2 }}
          {{ $colorIndex := mod (int (printf "0x%s" $hexColor)) 6 }}
          {{ $patternIndex := mod (int (printf "0x%s" $hexPattern)) 4 }}

          {{ $gradients := slice
            "from-blue-500/20 to-purple-500/10"
            "from-green-500/20 to-teal-500/10"
            "from-orange-500/20 to-red-500/10"
            "from-purple-500/20 to-pink-500/10"
            "from-teal-500/20 to-cyan-500/10"
            "from-rose-500/20 to-orange-500/10"
          }}


          <div
            class="{{ index $gradients $colorIndex }} relative aspect-[2/1] overflow-hidden bg-gradient-to-br">
            {{ if eq $patternIndex 0 }}
              <!-- 圆形图案 -->
              <div class="absolute inset-0 flex items-center justify-center">
                <div
                  class="flex h-16 w-16 items-center justify-center rounded-full border border-white/30 bg-white/20 backdrop-blur-sm">
                  <span class="text-xl font-bold text-white"
                    >{{ substr .Title 0 1 | upper }}</span
                  >
                </div>
              </div>
            {{ else if eq $patternIndex 1 }}
              <!-- 方形图案 -->
              <div class="absolute inset-0 flex items-center justify-center">
                <div
                  class="flex h-16 w-16 rotate-12 items-center justify-center rounded-xl border border-white/30 bg-white/20 backdrop-blur-sm">
                  <span class="text-xl font-bold text-white"
                    >{{ substr .Title 0 1 | upper }}</span
                  >
                </div>
              </div>
            {{ else if eq $patternIndex 2 }}
              <!-- 六边形图案 -->
              <div class="absolute inset-0 flex items-center justify-center">
                <div
                  class="flex h-16 w-16 items-center justify-center border border-white/30 bg-white/20 backdrop-blur-sm"
                  style="clip-path: polygon(50% 0%, 100% 25%, 100% 75%, 50% 100%, 0% 75%, 0% 25%);">
                  <span class="text-xl font-bold text-white"
                    >{{ substr .Title 0 1 | upper }}</span
                  >
                </div>
              </div>
            {{ else }}
              <!-- 菱形图案 -->
              <div class="absolute inset-0 flex items-center justify-center">
                <div
                  class="flex h-16 w-16 rotate-45 items-center justify-center rounded-lg border border-white/30 bg-white/20 backdrop-blur-sm">
                  <span class="-rotate-45 text-xl font-bold text-white"
                    >{{ substr .Title 0 1 | upper }}</span
                  >
                </div>
              </div>
            {{ end }}


            <!-- 装饰元素 -->
            <div
              class="absolute top-4 right-4 h-8 w-8 rounded-full bg-white/10"></div>
            <div
              class="absolute bottom-4 left-4 h-6 w-6 rounded-full bg-white/15"></div>
          </div>
        {{ end }}
      </div>

      <!-- 桌面端右侧封面 -->
      <div
        class="absolute top-0 right-0 hidden h-full w-64 transition-opacity duration-300 group-hover:opacity-90 md:block">
        {{ if .Params.cover }}
          <img
            src="{{ .Params.cover }}"
            alt="{{ .Title }}"
            class="h-full w-full object-cover"
            loading="lazy" />
        {{ else }}
          <!-- 桌面端动态默认装饰图案 -->
          {{ $hash := md5 .Title }}
          {{ $hexColor := substr $hash 0 2 }}
          {{ $hexPattern := substr $hash 2 2 }}
          {{ $colorIndex := mod (int (printf "0x%s" $hexColor)) 6 }}
          {{ $patternIndex := mod (int (printf "0x%s" $hexPattern)) 4 }}

          {{ $gradients := slice
            "from-blue-500/25 to-blue-500/8"
            "from-green-500/25 to-green-500/8"
            "from-orange-500/25 to-orange-500/8"
            "from-purple-500/25 to-purple-500/8"
            "from-teal-500/25 to-teal-500/8"
            "from-rose-500/25 to-rose-500/8"
          }}


          <div
            class="{{ index $gradients $colorIndex }} relative h-full w-full bg-gradient-to-br">
            {{ if eq $patternIndex 0 }}
              <!-- 圆形装饰 -->
              <div
                class="absolute top-8 right-8 h-24 w-24 rounded-full bg-white/15"></div>
              <div
                class="absolute top-20 right-16 h-12 w-12 rounded-full bg-white/25"></div>
              <div
                class="absolute right-6 bottom-16 h-20 w-20 rounded-full bg-white/12"></div>
              <div
                class="absolute right-12 bottom-8 h-10 w-10 rounded-full bg-white/20"></div>
            {{ else if eq $patternIndex 1 }}
              <!-- 方形装饰 -->
              <div
                class="absolute top-8 right-8 h-20 w-20 rotate-12 rounded-lg bg-white/15"></div>
              <div
                class="absolute top-24 right-20 h-10 w-10 rotate-45 rounded-md bg-white/25"></div>
              <div
                class="absolute right-6 bottom-16 h-16 w-16 -rotate-12 rounded-xl bg-white/12"></div>
              <div
                class="absolute right-16 bottom-8 h-8 w-8 rotate-45 rounded bg-white/20"></div>
            {{ else if eq $patternIndex 2 }}
              <!-- 混合装饰 -->
              <div
                class="absolute top-6 right-6 h-16 w-16 rounded-full bg-white/15"></div>
              <div
                class="absolute top-16 right-20 h-12 w-12 rotate-45 rounded-lg bg-white/25"></div>
              <div
                class="absolute right-4 bottom-20 h-20 w-20 rounded-full bg-white/12"></div>
              <div
                class="absolute right-14 bottom-6 h-8 w-8 rotate-12 rounded-md bg-white/20"></div>
              <div
                class="absolute top-32 right-12 h-6 w-6 rounded-full bg-white/30"></div>
            {{ else }}
              <!-- 线性装饰 -->
              <div
                class="absolute top-8 right-4 h-32 w-4 rotate-12 rounded-full bg-white/15"></div>
              <div
                class="absolute top-16 right-12 h-24 w-6 -rotate-12 rounded-full bg-white/20"></div>
              <div
                class="absolute right-8 bottom-16 h-20 w-8 rotate-45 rounded-full bg-white/12"></div>
              <div
                class="absolute right-16 bottom-8 h-16 w-3 -rotate-45 rounded-full bg-white/25"></div>
            {{ end }}
          </div>
        {{ end }}
      </div>

      <!-- 桌面端悬浮时的填充颜色遮罩 -->
      <div
        class="post-card-overlay absolute top-0 right-0 hidden h-full w-64 opacity-0 transition-opacity duration-300 group-hover:opacity-100 md:block"></div>

      <!-- 文章内容区域 -->
      <div
        class="relative z-10 flex flex-1 flex-col justify-between p-6 md:pr-72">
        <!-- 主要内容 -->
        <div class="space-y-3">
          <!-- 发布日期 -->
          <div class="text-muted-foreground text-xs font-medium">
            {{ .Date.Format (i18n "time.date_format" | default "2006年01月02日") }}
          </div>

          <!-- 文章标题 -->
          <h3
            class="text-foreground group-hover:text-primary line-clamp-2 text-lg leading-tight font-semibold transition-colors duration-200">
            {{ .Title }}
          </h3>

          <!-- 文章摘要 -->
          {{ if .Summary }}
            <p
              class="text-muted-foreground line-clamp-2 text-sm leading-relaxed">
              {{ .Summary | plainify | truncate 160 }}
            </p>
          {{ end }}
        </div>

        <!-- 底部元信息 -->
        <div
          class="text-muted-foreground mt-4 flex flex-wrap items-center gap-3 text-sm">
          <!-- 阅读时间 -->
          {{ if .ReadingTime }}
            <div
              class="bg-muted/50 border-muted/30 flex items-center gap-1.5 rounded-md border px-2 py-1">
              {{ partial "icon.html" (dict "name" "clock" "size" "sm" "ariaLabel" (i18n "post.reading_time")) }}
              <span class="text-sm font-medium"
                >{{ .ReadingTime }}
                {{ i18n "time.minute" | default "min" }}</span
              >
            </div>
          {{ end }}


          <!-- 文章分类 -->
          {{ if .Params.categories }}
            <div
              class="bg-muted/50 border-muted/30 flex items-center gap-1.5 rounded-md border px-2 py-1">
              {{ partial "icon.html" (dict "name" "folder" "size" "sm" "ariaLabel" (i18n "post.categories")) }}
              <span class="text-sm font-medium"
                >{{ index .Params.categories 0 }}</span
              >
            </div>
          {{ end }}


          <!-- 文章标签 -->
          {{ if .Params.tags }}
            <div class="flex items-center gap-2">
              {{ range first 3 .Params.tags }}
                <span
                  class="bg-muted/50 border-muted/30 inline-flex items-center gap-1.5 rounded-md border px-2 py-1 text-sm font-medium">
                  {{ partial "icon.html" (dict "name" "tag" "size" "sm" "ariaLabel" (i18n "post.tags")) }}
                  {{ . }}
                </span>
              {{ end }}
              {{ if gt (len .Params.tags) 3 }}
                <span class="text-muted-foreground/70 text-sm font-medium"
                  >+{{ sub (len .Params.tags) 3 }}</span
                >
              {{ end }}
            </div>
          {{ end }}

        </div>
      </div>
    </div>
  </a>
</article>
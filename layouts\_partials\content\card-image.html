{{/* 
  文章卡片图片组件 - 处理封面图片和占位图的统一逻辑
  
  参数：
  - .page: 页面对象
  - .type: 图片类型 ("mobile", "desktop", "related")
  - .coverImage: 封面图片URL
  - .gradients: 渐变色数组
  - .colorIndex: 颜色索引
*/}}

{{ $page := .page }}
{{ $type := .type }}
{{ $coverImage := .coverImage }}
{{ $gradients := .gradients }}
{{ $colorIndex := .colorIndex }}

{{/* 根据标题生成装饰图案索引 */}}
{{ $hash := md5 $page.Title }}
{{ $hexPattern := substr $hash 2 2 }}
{{ $patternIndex := mod (int (printf "0x%s" $hexPattern)) 4 }}

{{/* 根据类型设置不同的样式参数 */}}
{{ $containerClass := "" }}
{{ $aspectClass := "" }}

{{ if eq $type "mobile" }}
  {{ $containerClass = "aspect-[2/1] overflow-hidden" }}
  {{ $aspectClass = "relative aspect-[2/1] overflow-hidden bg-gradient-to-br" }}
{{ else if eq $type "desktop" }}
  {{ $containerClass = "h-full w-full" }}
  {{ $aspectClass = "relative h-full w-full bg-gradient-to-br" }}
{{ else if eq $type "related" }}
  {{ $containerClass = "relative aspect-[16/9] flex-shrink-0 overflow-hidden" }}
  {{ $aspectClass = "flex h-full w-full items-center justify-center bg-gradient-to-br" }}
{{ end }}

<div class="{{ $containerClass }}">
  {{ if $coverImage }}
    <img
      src="{{ $coverImage }}"
      alt="{{ $page.Title }}"
      class="h-full w-full object-cover{{ if ne $type "desktop" }} transition-transform duration-300 group-hover:scale-105{{ end }}"
      loading="lazy"
      onerror="this.style.display='none'; this.nextElementSibling.style.display='{{ if eq $type "related" }}flex{{ else }}block{{ end }}';" />
    <!-- 图片加载失败时的占位图 -->
    <div class="{{ index $gradients $colorIndex }} {{ $aspectClass }}" style="display: none;">
      <div class="absolute inset-0">
        <!-- 根据图案索引显示不同的装饰图案 -->
        {{ if eq $patternIndex 0 }}
          <!-- 图案1：几何圆形 -->
          <div class="absolute inset-0 flex items-center justify-center">
            <div class="h-24 w-24 rounded-full bg-white/20 backdrop-blur-sm"></div>
            <div class="absolute top-1/4 right-1/4 h-8 w-8 rounded-full bg-white/15"></div>
            <div class="absolute bottom-1/4 left-1/4 h-12 w-12 rounded-full bg-white/10"></div>
          </div>
        {{ else if eq $patternIndex 1 }}
          <!-- 图案2：菱形网格 -->
          <div class="absolute inset-0 flex items-center justify-center">
            <div class="h-16 w-16 rotate-45 bg-white/20 backdrop-blur-sm"></div>
            <div class="absolute top-1/3 right-1/3 h-6 w-6 rotate-45 bg-white/15"></div>
            <div class="absolute bottom-1/3 left-1/3 h-8 w-8 rotate-45 bg-white/10"></div>
            <div class="absolute top-2/3 left-1/2 h-4 w-4 rotate-45 bg-white/12"></div>
          </div>
        {{ else if eq $patternIndex 2 }}
          <!-- 图案3：波浪线条 -->
          <div class="absolute inset-0 overflow-hidden">
            <div class="absolute top-1/4 left-0 h-1 w-full bg-white/20 transform -skew-y-12"></div>
            <div class="absolute top-1/2 left-0 h-2 w-full bg-white/15 transform skew-y-6"></div>
            <div class="absolute top-3/4 left-0 h-1 w-full bg-white/10 transform -skew-y-3"></div>
            <div class="absolute inset-0 flex items-center justify-center">
              <div class="h-20 w-20 rounded-full bg-white/10 backdrop-blur-sm"></div>
            </div>
          </div>
        {{ else }}
          <!-- 图案4：星形散布 -->
          <div class="absolute inset-0">
            <div class="absolute top-1/4 left-1/4 h-3 w-3 bg-white/20 transform rotate-45"></div>
            <div class="absolute top-1/3 right-1/4 h-2 w-2 bg-white/25 transform rotate-45"></div>
            <div class="absolute bottom-1/3 left-1/3 h-4 w-4 bg-white/15 transform rotate-45"></div>
            <div class="absolute bottom-1/4 right-1/3 h-2 w-2 bg-white/20 transform rotate-45"></div>
            <div class="absolute top-2/3 left-2/3 h-3 w-3 bg-white/18 transform rotate-45"></div>
            <div class="absolute inset-0 flex items-center justify-center">
              <div class="h-16 w-16 rounded-full bg-white/15 backdrop-blur-sm"></div>
            </div>
          </div>
        {{ end }}
      </div>
    </div>
  {{ else }}
    <!-- 统一占位图 -->
    <div class="{{ index $gradients $colorIndex }} {{ $aspectClass }}">
      <div class="absolute inset-0">
        <!-- 根据图案索引显示不同的装饰图案 -->
        {{ if eq $patternIndex 0 }}
          <!-- 图案1：几何圆形 -->
          <div class="absolute inset-0 flex items-center justify-center">
            <div class="h-24 w-24 rounded-full bg-white/20 backdrop-blur-sm"></div>
            <div class="absolute top-1/4 right-1/4 h-8 w-8 rounded-full bg-white/15"></div>
            <div class="absolute bottom-1/4 left-1/4 h-12 w-12 rounded-full bg-white/10"></div>
          </div>
        {{ else if eq $patternIndex 1 }}
          <!-- 图案2：菱形网格 -->
          <div class="absolute inset-0 flex items-center justify-center">
            <div class="h-16 w-16 rotate-45 bg-white/20 backdrop-blur-sm"></div>
            <div class="absolute top-1/3 right-1/3 h-6 w-6 rotate-45 bg-white/15"></div>
            <div class="absolute bottom-1/3 left-1/3 h-8 w-8 rotate-45 bg-white/10"></div>
            <div class="absolute top-2/3 left-1/2 h-4 w-4 rotate-45 bg-white/12"></div>
          </div>
        {{ else if eq $patternIndex 2 }}
          <!-- 图案3：波浪线条 -->
          <div class="absolute inset-0 overflow-hidden">
            <div class="absolute top-1/4 left-0 h-1 w-full bg-white/20 transform -skew-y-12"></div>
            <div class="absolute top-1/2 left-0 h-2 w-full bg-white/15 transform skew-y-6"></div>
            <div class="absolute top-3/4 left-0 h-1 w-full bg-white/10 transform -skew-y-3"></div>
            <div class="absolute inset-0 flex items-center justify-center">
              <div class="h-20 w-20 rounded-full bg-white/10 backdrop-blur-sm"></div>
            </div>
          </div>
        {{ else }}
          <!-- 图案4：星形散布 -->
          <div class="absolute inset-0">
            <div class="absolute top-1/4 left-1/4 h-3 w-3 bg-white/20 transform rotate-45"></div>
            <div class="absolute top-1/3 right-1/4 h-2 w-2 bg-white/25 transform rotate-45"></div>
            <div class="absolute bottom-1/3 left-1/3 h-4 w-4 bg-white/15 transform rotate-45"></div>
            <div class="absolute bottom-1/4 right-1/3 h-2 w-2 bg-white/20 transform rotate-45"></div>
            <div class="absolute top-2/3 left-2/3 h-3 w-3 bg-white/18 transform rotate-45"></div>
            <div class="absolute inset-0 flex items-center justify-center">
              <div class="h-16 w-16 rounded-full bg-white/15 backdrop-blur-sm"></div>
            </div>
          </div>
        {{ end }}
      </div>
    </div>
  {{ end }}
</div>

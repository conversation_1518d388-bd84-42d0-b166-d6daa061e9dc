{{/* 文章列表组件

  显示文章卡片列表

  @context {slice} .posts 文章列表
  @context {string} .title 列表标题 (可选)
  @context {bool} .showTitle 是否显示标题 (默认: false)
  @context {bool} .showViewAll 是否显示"查看所有"按钮 (默认: false)
  @context {string} .viewAllUrl "查看所有"按钮的链接 (默认: /posts/)
*/}}

{{ $posts := .posts }}
{{ $title := .title | default "" }}
{{ $showTitle := .showTitle | default false }}
{{ $showViewAll := .showViewAll | default false }}
{{ $viewAllUrl := .viewAllUrl | default ("/posts/" | relLangURL) }}

{{ if $posts }}
  <section class="post-list">
    <!-- 列表标题 -->
    {{ if and $showTitle $title }}
      <div class="mb-6 flex items-center justify-between">
        <h2 class="text-foreground text-2xl font-bold">{{ $title }}</h2>
      </div>
    {{ end }}


    <!-- 文章卡片列表 -->
    <div class="space-y-4">
      {{ range $posts }}
        {{ partial "content/post-card.html" . }}
      {{ end }}
    </div>

    <!-- 查看所有文章按钮 (底部) -->
    {{ if $showViewAll }}
      <div class="mt-8 text-center">
        <a
          href="{{ $viewAllUrl | relURL }}"
          class="bg-primary text-primary-foreground hover:bg-primary/90 focus:ring-primary/20 inline-flex items-center gap-2 rounded-xl px-6 py-3 font-medium shadow-sm transition-all duration-300 ease-out hover:-translate-y-1 hover:scale-105 hover:shadow-lg focus:ring-2 focus:outline-none active:scale-95">
          {{ partial "features/icon.html" (dict "name" "posts" "size" "md" "ariaLabel" (i18n "nav.posts")) }}
          <span>{{ i18n "home.view_all_posts" | default "查看所有文章" }}</span>
          {{ partial "features/icon.html" (dict "name" "arrow-right" "size" "sm" "ariaLabel" "") }}
        </a>
      </div>
    {{ end }}

  </section>
{{ else }}
  <!-- 空状态 -->
  <div class="py-12 text-center">
    <div
      class="bg-muted/50 mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full">
      {{ partial "features/icon.html" (dict "name" "folder" "size" "lg" "ariaLabel" "") }}
    </div>
    <h3 class="text-foreground mb-2 text-lg font-medium">
      {{ i18n "post.no_posts" | default "暂无文章" }}
    </h3>
    <p class="text-muted-foreground">
      {{ i18n "post.no_posts_desc" | default "还没有发布任何文章，请稍后再来查看。" }}
    </p>
  </div>
{{ end }}

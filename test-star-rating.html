<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>星星评级测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-case {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .star-rating {
            color: #fbbf24;
            font-size: 18px;
            margin-left: 10px;
        }
        .score {
            font-weight: bold;
            color: #333;
        }
    </style>
</head>
<body>
    <h1>搜索功能星星评级测试</h1>
    <p>测试不同匹配度分数对应的星星数量显示：</p>
    
    <div id="test-results"></div>

    <script>
        // 复制搜索功能中的星星评级函数
        function generateStarRating(score) {
            if (!score || score <= 0) return "";
            
            // 定义星星数量的阈值
            // 基于当前评分系统：标题匹配10分，摘要匹配5分，内容匹配1分
            let starCount;
            if (score >= 20) {
                starCount = 5; // 高匹配度：多个关键词在标题中匹配
            } else if (score >= 15) {
                starCount = 4; // 较高匹配度：标题+摘要匹配
            } else if (score >= 10) {
                starCount = 3; // 中等匹配度：标题匹配或多个摘要匹配
            } else if (score >= 5) {
                starCount = 2; // 较低匹配度：摘要匹配
            } else {
                starCount = 1; // 低匹配度：仅内容匹配
            }
            
            // 生成星星字符串
            return "★".repeat(starCount);
        }

        // 测试用例
        const testCases = [
            { score: 25, description: "高匹配度：多个关键词在标题中匹配" },
            { score: 20, description: "高匹配度边界值" },
            { score: 18, description: "较高匹配度：标题+摘要匹配" },
            { score: 15, description: "较高匹配度边界值" },
            { score: 12, description: "中等匹配度：标题匹配" },
            { score: 10, description: "中等匹配度边界值" },
            { score: 8, description: "较低匹配度：摘要匹配" },
            { score: 5, description: "较低匹配度边界值" },
            { score: 3, description: "低匹配度：仅内容匹配" },
            { score: 1, description: "最低匹配度" },
            { score: 0, description: "无匹配" },
        ];

        // 渲染测试结果
        const resultsContainer = document.getElementById('test-results');
        
        testCases.forEach(testCase => {
            const starRating = generateStarRating(testCase.score);
            const div = document.createElement('div');
            div.className = 'test-case';
            div.innerHTML = `
                <div>
                    <span class="score">分数: ${testCase.score}</span>
                    <span class="star-rating">${starRating || '无星星'}</span>
                </div>
                <div style="margin-top: 5px; color: #666; font-size: 14px;">
                    ${testCase.description}
                </div>
            `;
            resultsContainer.appendChild(div);
        });
    </script>
</body>
</html>

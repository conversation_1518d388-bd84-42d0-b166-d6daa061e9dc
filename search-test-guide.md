# 搜索功能星星评级测试指南

## 测试目标
验证搜索功能中根据匹配度动态显示不同数量星星的功能是否正常工作。

## 测试步骤

### 1. 打开网站
访问 http://localhost:1314/

### 2. 打开搜索功能
- 点击搜索图标或按 Ctrl+K / Cmd+K 快捷键
- 搜索模态框应该出现

### 3. 测试不同匹配度的搜索

#### 高匹配度测试（预期：5颗星 ★★★★★）
- 搜索关键词：在标题中出现多次的词汇
- 例如：搜索 "Hugo" 或 "Guide"

#### 较高匹配度测试（预期：4颗星 ★★★★）
- 搜索关键词：在标题和摘要中都出现的词汇
- 例如：搜索 "theme" 或 "config"

#### 中等匹配度测试（预期：3颗星 ★★★）
- 搜索关键词：主要在标题中出现的词汇
- 例如：搜索 "installation" 或 "setup"

#### 较低匹配度测试（预期：2颗星 ★★）
- 搜索关键词：主要在摘要中出现的词汇
- 例如：搜索 "example" 或 "demo"

#### 低匹配度测试（预期：1颗星 ★）
- 搜索关键词：仅在内容中出现的词汇
- 例如：搜索 "content" 或 "page"

## 评分系统说明

当前的评分系统基于以下权重：
- 标题匹配：10分
- 摘要匹配：5分
- 内容匹配：1分

星星数量阈值：
- 5颗星：分数 ≥ 20（高匹配度）
- 4颗星：分数 ≥ 15（较高匹配度）
- 3颗星：分数 ≥ 10（中等匹配度）
- 2颗星：分数 ≥ 5（较低匹配度）
- 1颗星：分数 < 5（低匹配度）

## 预期结果

1. 搜索结果应该显示星星评级而不是数字分数
2. 星星应该显示为黄色（#f59e0b）
3. 星星数量应该根据匹配度合理分布
4. 高相关性的结果应该显示更多星星
5. 搜索功能应该保持原有的所有其他功能

## 验证要点

- [ ] 星星显示正常（不是数字分数）
- [ ] 星星颜色为黄色
- [ ] 不同匹配度显示不同数量的星星
- [ ] 搜索结果排序仍然正确
- [ ] 搜索高亮功能正常
- [ ] 键盘导航功能正常
- [ ] 点击搜索结果能正常跳转
